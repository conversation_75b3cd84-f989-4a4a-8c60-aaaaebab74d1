import puppeteer from 'puppeteer'

const APP_URL = 'http://localhost:8080'
const INVITATION_TOKEN = '8d18a33d-7bb0-46fa-9486-a4c6a1ea1787'

async function browserTest() {
  let browser
  
  try {
    console.log('🚀 Starting browser automation test...')
    
    browser = await puppeteer.launch({ 
      headless: false,
      defaultViewport: { width: 1280, height: 720 },
      slowMo: 300,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })
    
    const page = await browser.newPage()
    
    // Test 1: Login with your credentials
    console.log('\n1. Testing login...')
    await page.goto(APP_URL, { waitUntil: 'networkidle0' })
    
    await page.waitForSelector('input[type="email"]', { timeout: 10000 })
    await page.type('input[type="email"]', '<EMAIL>')
    await page.type('input[type="password"]', 'Newsig1!!!')
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    const loginUrl = page.url()
    console.log('✅ Login URL:', loginUrl)
    
    if (loginUrl.includes('dashboard') || loginUrl.includes('teams')) {
      console.log('✅ Login successful!')
      
      // Take screenshot
      await page.screenshot({ path: 'login-success.png', fullPage: true })
      console.log('📸 Screenshot: login-success.png')
      
      // Check page content
      const pageText = await page.evaluate(() => document.body.textContent)
      console.log('Contains profile name:', pageText.includes('Andrew'))
      console.log('Contains team data:', pageText.toLowerCase().includes('team'))
      
    } else {
      console.log('❌ Login issue')
      await page.screenshot({ path: 'login-issue.png', fullPage: true })
    }
    
    // Test 2: Test invitation flow
    console.log('\n2. Testing invitation link...')
    
    // Navigate to invitation
    const invitationUrl = `${APP_URL}/#/invite?token=${INVITATION_TOKEN}`
    await page.goto(invitationUrl, { waitUntil: 'networkidle0' })
    await page.waitForTimeout(2000)
    
    await page.screenshot({ path: 'invitation-page.png', fullPage: true })
    console.log('📸 Screenshot: invitation-page.png')
    
    const inviteText = await page.evaluate(() => document.body.textContent)
    console.log('Invitation page contains expected content:', 
      inviteText.includes('invitation') || inviteText.includes('team'))
    
    // Test 3: Try registration
    console.log('\n3. Testing registration...')
    
    // Look for registration tab/button
    try {
      await page.click('button:has-text("Register"), [role="tab"]:has-text("Register"), a:has-text("Register")')
      await page.waitForTimeout(1000)
    } catch (e) {
      console.log('Registration tab not found, trying direct form access')
    }
    
    // Fill registration form
    try {
      const emailInput = await page.$('input[type="email"]')
      const passwordInput = await page.$('input[type="password"]')
      
      if (emailInput && passwordInput) {
        // Clear email field and enter test email
        await emailInput.click({ clickCount: 3 })
        await emailInput.type('<EMAIL>')
        
        // Look for first name and last name fields
        const firstNameInput = await page.$('input[placeholder*="first"], input[name*="first"], input[id*="first"]')
        const lastNameInput = await page.$('input[placeholder*="last"], input[name*="last"], input[id*="last"]')
        
        if (firstNameInput) {
          await firstNameInput.type('Test')
        }
        if (lastNameInput) {
          await lastNameInput.type('User')
        }
        
        await passwordInput.type('testpassword123')
        
        console.log('✅ Registration form filled')
        await page.screenshot({ path: 'registration-form.png', fullPage: true })
        console.log('📸 Screenshot: registration-form.png')
        
        // Submit form
        const submitButton = await page.$('button[type="submit"], button:has-text("Register"), button:has-text("Create")')
        if (submitButton) {
          await submitButton.click()
          await page.waitForTimeout(3000)
          
          console.log('✅ Registration submitted')
          await page.screenshot({ path: 'registration-result.png', fullPage: true })
          console.log('📸 Screenshot: registration-result.png')
          
          const resultUrl = page.url()
          console.log('Result URL:', resultUrl)
        }
      }
    } catch (e) {
      console.log('Error with registration:', e.message)
    }
    
    console.log('\n✅ Browser test completed!')
    console.log('\n📋 Check the screenshots:')
    console.log('- login-success.png - What you see after login')
    console.log('- invitation-page.png - The invitation page')
    console.log('- registration-form.png - Registration form')
    console.log('- registration-result.png - After registration')
    
  } catch (error) {
    console.error('❌ Browser test failed:', error)
  } finally {
    if (browser) {
      setTimeout(() => browser.close(), 5000) // Keep browser open for 5 seconds
    }
  }
}

browserTest().catch(console.error)