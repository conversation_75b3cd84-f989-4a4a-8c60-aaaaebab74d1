import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.2m5CgPGaWNdtxczMbEyRKx2kkdvLbJFHw3eKE2wGNp4'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function clearMembersExceptSpecified() {
  console.log('🚀 Starting database cleanup...')
  console.log('📋 Database backup already created: database_backup_20250708_152902.sql')
  console.log('')

  // Accounts to keep
  const keepEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ]

  try {
    // First, show what we have
    console.log('📊 Current state before cleanup:')
    console.log('================================')
    
    const { data: allProfiles } = await supabase
      .from('profiles')
      .select('id, email, first_name, last_name, role, is_super_admin, created_at')
      .order('email')
    
    console.log('All profiles:')
    allProfiles?.forEach(profile => {
      console.log(`  - ${profile.email} (${profile.first_name} ${profile.last_name}) - ${profile.role}${profile.is_super_admin ? ' [SUPER ADMIN]' : ''}`)
    })
    console.log('')

    const { data: allTeamMembers } = await supabase
      .from('team_members')
      .select(`
        id, user_id, team_id, role,
        profiles!inner(email, first_name, last_name),
        teams!inner(name)
      `)
      .order('profiles.email')
    
    console.log('All team members:')
    allTeamMembers?.forEach(member => {
      console.log(`  - ${member.profiles.email} in "${member.teams.name}" as ${member.role}`)
    })
    console.log('')

    // Get IDs of accounts to keep
    const { data: accountsToKeep } = await supabase
      .from('profiles')
      .select('id, email')
      .in('email', keepEmails)
    
    if (!accountsToKeep || accountsToKeep.length === 0) {
      console.error('❌ No accounts found to keep! Aborting for safety.')
      return
    }

    console.log('🔒 Accounts to keep:')
    accountsToKeep.forEach(account => {
      console.log(`  - ${account.email} (ID: ${account.id})`)
    })
    console.log('')

    const keepIds = accountsToKeep.map(a => a.id)

    // Delete team members who are NOT in the keep list
    console.log('🗑️  Deleting team members not in keep list...')
    const { data: deletedMembers, error: deleteMembersError } = await supabase
      .from('team_members')
      .delete()
      .not('user_id', 'in', `(${keepIds.join(',')})`)
      .select('id')

    if (deleteMembersError) {
      console.error('❌ Error deleting team members:', deleteMembersError)
      return
    }

    console.log(`✅ Deleted ${deletedMembers?.length || 0} team members`)

    // Delete team invitations for emails not in the keep list
    console.log('🗑️  Deleting team invitations not in keep list...')
    const { data: deletedInvitations, error: deleteInvitationsError } = await supabase
      .from('team_invitations')
      .delete()
      .not('email', 'in', `(${keepEmails.map(e => `'${e}'`).join(',')})`)
      .select('id')

    if (deleteInvitationsError) {
      console.error('❌ Error deleting team invitations:', deleteInvitationsError)
      return
    }

    console.log(`✅ Deleted ${deletedInvitations?.length || 0} team invitations`)

    // Delete user permissions for users not in the keep list
    console.log('🗑️  Deleting user permissions not in keep list...')
    const { data: deletedPermissions, error: deletePermissionsError } = await supabase
      .from('user_permissions')
      .delete()
      .not('user_id', 'in', `(${keepIds.join(',')})`)
      .select('id')

    if (deletePermissionsError) {
      console.error('❌ Error deleting user permissions:', deletePermissionsError)
      return
    }

    console.log(`✅ Deleted ${deletedPermissions?.length || 0} user permissions`)

    // Delete profiles that are not in the keep list
    console.log('🗑️  Deleting profiles not in keep list...')
    const { data: deletedProfiles, error: deleteProfilesError } = await supabase
      .from('profiles')
      .delete()
      .not('email', 'in', `(${keepEmails.map(e => `'${e}'`).join(',')})`)
      .select('email')

    if (deleteProfilesError) {
      console.error('❌ Error deleting profiles:', deleteProfilesError)
      return
    }

    console.log(`✅ Deleted ${deletedProfiles?.length || 0} profiles:`)
    deletedProfiles?.forEach(profile => {
      console.log(`  - ${profile.email}`)
    })
    console.log('')

    // Show what remains
    console.log('📊 Final state after cleanup:')
    console.log('=============================')
    
    const { data: remainingProfiles } = await supabase
      .from('profiles')
      .select('id, email, first_name, last_name, role, is_super_admin, created_at')
      .order('email')
    
    console.log('Remaining profiles:')
    remainingProfiles?.forEach(profile => {
      console.log(`  - ${profile.email} (${profile.first_name} ${profile.last_name}) - ${profile.role}${profile.is_super_admin ? ' [SUPER ADMIN]' : ''}`)
    })
    console.log('')

    const { data: remainingTeamMembers } = await supabase
      .from('team_members')
      .select(`
        id, user_id, team_id, role,
        profiles!inner(email, first_name, last_name),
        teams!inner(name)
      `)
      .order('profiles.email')
    
    console.log('Remaining team members:')
    remainingTeamMembers?.forEach(member => {
      console.log(`  - ${member.profiles.email} in "${member.teams.name}" as ${member.role}`)
    })
    console.log('')

    console.log('🎉 Database cleanup completed successfully!')
    console.log('📋 Backup available at: database_backup_20250708_152902.sql')

  } catch (error) {
    console.error('❌ Unexpected error during cleanup:', error)
  }
}

clearMembersExceptSpecified().catch(console.error)