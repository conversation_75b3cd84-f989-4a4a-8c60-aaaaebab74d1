-- Database cleanup script to remove all members except specified accounts
-- Backup created: database_backup_20250708_152902.sql

-- Keep these accounts:
-- <EMAIL> (Superadmin)
-- <EMAIL> (Property Manager) 
-- <EMAIL> (Property Manager)

BEGIN;

-- First, let's see what we have
SELECT 'Current profiles:' as info;
SELECT id, email, first_name, last_name, role, is_super_admin, created_at 
FROM profiles 
ORDER BY email;

SELECT 'Current team members:' as info;
SELECT tm.id, tm.user_id, tm.team_id, tm.role, p.email, p.first_name, p.last_name, t.name as team_name
FROM team_members tm
JOIN profiles p ON tm.user_id = p.id
JOIN teams t ON tm.team_id = t.id
ORDER BY p.email;

-- Store the IDs of accounts to keep
WITH accounts_to_keep AS (
  SELECT id FROM profiles WHERE email IN (
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>'
  )
)
-- Delete team members who are NOT in the accounts to keep list
DELETE FROM team_members 
WHERE user_id NOT IN (SELECT id FROM accounts_to_keep);

-- Delete team invitations for users not in the keep list  
WITH accounts_to_keep AS (
  SELECT id FROM profiles WHERE email IN (
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>'
  )
)
DELETE FROM team_invitations 
WHERE email NOT IN (
  '<EMAIL>',
  '<EMAIL>', 
  '<EMAIL>'
);

-- Delete user permissions for users not in the keep list
WITH accounts_to_keep AS (
  SELECT id FROM profiles WHERE email IN (
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>'
  )
)
DELETE FROM user_permissions 
WHERE user_id NOT IN (SELECT id FROM accounts_to_keep);

-- Delete profiles that are not in the keep list
DELETE FROM profiles 
WHERE email NOT IN (
  '<EMAIL>',
  '<EMAIL>', 
  '<EMAIL>'
);

-- Show what remains
SELECT 'Remaining profiles:' as info;
SELECT id, email, first_name, last_name, role, is_super_admin, created_at 
FROM profiles 
ORDER BY email;

SELECT 'Remaining team members:' as info;
SELECT tm.id, tm.user_id, tm.team_id, tm.role, p.email, p.first_name, p.last_name, t.name as team_name
FROM team_members tm
JOIN profiles p ON tm.user_id = p.id
JOIN teams t ON tm.team_id = t.id
ORDER BY p.email;

-- ROLLBACK; -- Uncomment this line if you want to test without actually making changes
COMMIT;