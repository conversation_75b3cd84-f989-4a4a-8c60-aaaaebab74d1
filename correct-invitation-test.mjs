import { createClient } from '@supabase/supabase-js'
import puppeteer from 'puppeteer'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)
const APP_URL = 'http://localhost:8081'  // Correct port!

async function testWithCorrectPort() {
  console.log('🔍 Testing with correct port (8081)')
  console.log('=====================================')
  
  // First, create a fresh invitation
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'Newsig1!!!'
  })
  
  if (authError) {
    console.error('❌ Login failed:', authError.message)
    return
  }
  
  console.log('✅ Login successful')
  
  // Get teams
  const { data: teams } = await supabase
    .from('teams')
    .select('*')
  
  if (teams && teams.length > 0) {
    // Create a fresh invitation
    const invitationToken = crypto.randomUUID()
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7)
    
    const { error: inviteError } = await supabase
      .from('team_invitations')
      .insert({
        team_id: teams[0].id,
        email: '<EMAIL>',
        role: 'service_provider',
        invited_by: authData.user.id,
        token: invitationToken,
        expires_at: expiresAt.toISOString(),
        status: 'pending'
      })
    
    if (!inviteError) {
      console.log('✅ Fresh invitation created!')
      console.log(`🔗 CORRECT INVITATION LINK:`)
      console.log(`http://localhost:8081/#/invite?token=${invitationToken}`)
      
      // Now test the browser automation with correct port
      let browser
      try {
        browser = await puppeteer.launch({ 
          headless: false,
          defaultViewport: { width: 1280, height: 720 },
          slowMo: 500
        })
        
        const page = await browser.newPage()
        
        // Test 1: Navigate to login page
        console.log('\n1. Testing login page access...')
        await page.goto(`${APP_URL}/#/login`, { waitUntil: 'networkidle0' })
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        const hasEmailInput = await page.$('input[type="email"]') !== null
        const hasPasswordInput = await page.$('input[type="password"]') !== null
        
        console.log('✅ Login form found:', hasEmailInput && hasPasswordInput)
        await page.screenshot({ path: 'correct-login-page.png', fullPage: true })
        console.log('📸 Screenshot: correct-login-page.png')
        
        // Test 2: Test invitation link
        console.log('\n2. Testing invitation link...')
        await page.goto(`${APP_URL}/#/invite?token=${invitationToken}`, { waitUntil: 'networkidle0' })
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        const invitePageText = await page.evaluate(() => document.body.textContent)
        console.log('Invitation page loaded:', invitePageText.includes('invitation') || invitePageText.includes('team'))
        
        await page.screenshot({ path: 'correct-invitation-page.png', fullPage: true })
        console.log('📸 Screenshot: correct-invitation-page.png')
        
        console.log('\n🎯 Manual Testing Instructions:')
        console.log('1. Use the invitation link above')
        console.log('2. Register with: <EMAIL>')
        console.log('3. Password: testpassword123')
        console.log('4. Check if you can see team data after registration')
        
        // Keep browser open for manual testing
        console.log('\n⏳ Browser staying open for manual testing...')
        await new Promise(resolve => setTimeout(resolve, 60000)) // 1 minute
        
      } catch (error) {
        console.error('❌ Browser test failed:', error)
      } finally {
        if (browser) {
          await browser.close()
        }
      }
    }
  }
  
  await supabase.auth.signOut()
}

testWithCorrectPort().catch(console.error)