import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function debugServiceProviderTasks() {
  console.log('🔍 Debugging Service Provider Task Access')
  console.log('==========================================')
  
  try {
    // First, let's see what maintenance tasks exist
    console.log('\n📋 All maintenance tasks in database:')
    const { data: allTasks, error: tasksError } = await supabase
      .from('maintenance_tasks')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (tasksError) {
      console.error('❌ Error fetching tasks:', tasksError)
      return
    }
    
    console.log(`Found ${allTasks?.length || 0} total tasks`)
    allTasks?.forEach(task => {
      console.log(`  - Task: "${task.title}" | Property: ${task.property_id} | User: ${task.user_id} | Provider: ${task.provider_id} | Team: ${task.team_id}`)
    })
    
    // Check team memberships
    console.log('\n👥 Team memberships:')
    const { data: teamMembers, error: membersError } = await supabase
      .from('team_members')
      .select(`
        user_id,
        team_id,
        role,
        profiles!inner(email, first_name, last_name),
        teams!inner(name)
      `)
    
    if (membersError) {
      console.error('❌ Error fetching team members:', membersError)
    } else {
      teamMembers?.forEach(member => {
        console.log(`  - ${member.profiles.email} (${member.profiles.first_name} ${member.profiles.last_name}) is ${member.role} in team "${member.teams.name}"`)
      })
    }
    
    // Check team properties
    console.log('\n🏠 Team properties:')
    const { data: teamProperties, error: teamPropsError } = await supabase
      .from('team_properties')
      .select(`
        property_id,
        team_id,
        properties!inner(name, user_id),
        teams!inner(name)
      `)
    
    if (teamPropsError) {
      console.error('❌ Error fetching team properties:', teamPropsError)
    } else {
      teamProperties?.forEach(tp => {
        console.log(`  - Property "${tp.properties.name}" (${tp.property_id}) is assigned to team "${tp.teams.name}" (${tp.team_id})`)
      })
    }
    
    // Test service provider access (you'll need to replace with actual service provider email)
    const serviceProviderEmail = '<EMAIL>' // Replace with actual email
    
    console.log(`\n🔧 Testing access for service provider: ${serviceProviderEmail}`)
    
    // Sign in as service provider (you'll need to provide credentials)
    // const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    //   email: serviceProviderEmail,
    //   password: 'password' // Replace with actual password
    // })
    
    // if (authError) {
    //   console.error('❌ Failed to sign in as service provider:', authError)
    //   return
    // }
    
    // console.log('✅ Signed in as service provider')
    
    // // Test the RPC function
    // const { data: rpcTasks, error: rpcError } = await supabase.rpc('get_maintenance_tasks_for_user', {
    //   p_user_id: authData.user.id
    // })
    
    // if (rpcError) {
    //   console.error('❌ RPC function error:', rpcError)
    // } else {
    //   console.log(`📋 RPC returned ${rpcTasks?.length || 0} tasks for service provider`)
    //   rpcTasks?.forEach(task => {
    //     console.log(`  - "${task.title}" | Property: ${task.property_id} | Team: ${task.team_id}`)
    //   })
    // }
    
    console.log('\n💡 To complete this debug, you need to:')
    console.log('1. Sign in as a service provider')
    console.log('2. Check what tasks they can see')
    console.log('3. Verify if they have team membership for those properties')
    console.log('4. Apply the migration to fix the access control')
    
  } catch (error) {
    console.error('❌ Debug error:', error)
  }
}

debugServiceProviderTasks().catch(console.error)