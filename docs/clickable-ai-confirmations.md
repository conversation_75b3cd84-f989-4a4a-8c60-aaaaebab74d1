# Clickable AI Confirmation Messages

## Overview

The StayFu AI Assistant now supports clickable confirmation messages that allow users to navigate directly to created items. When the AI successfully creates or updates an item (maintenance task, purchase order, property, etc.), users can click on the confirmation toast to be taken directly to that item.

## Features

### Enhanced Toast Notifications
- **Success messages** now include a "View [item type]" button with an external link icon
- **Error messages** display as regular toast notifications without action buttons
- **Longer duration** for actionable toasts (8 seconds vs default) to give users time to click
- **Consistent styling** with the existing StayFu design system

### Supported Entity Types

| Entity Type | Navigation Path | Description |
|-------------|----------------|-------------|
| `maintenance_task` | `/maintenance?id={taskId}` | Opens maintenance page with task details |
| `purchase_order` | `/purchase-orders?id={orderId}` | Opens purchase orders page with specific order |
| `property` | `/properties/{propertyId}` | Opens property detail page |
| `inventory_item` | `/inventory?item={itemId}` | Opens inventory page filtered to specific item |
| `damage_report` | `/damages/{reportId}` | Opens damage report detail page |
| `team` | `/teams?team={teamId}` | Opens teams page filtered to specific team |

## Implementation

### Core Function

The main function `showAIConfirmationToast` is located in `src/lib/utils.ts`:

```typescript
export function showAIConfirmationToast(
  result: AICommandResult, 
  navigationConfig?: NavigationConfig
)
```

### Usage in Components

#### AiCommandCenter (Dashboard)
```typescript
// Use enhanced toast with navigation support
showAIConfirmationToast(data, {
  entityType: data.entityType || '',
  entityId: data.entityId,
  navigate
});
```

#### AiMaintenanceDialog
```typescript
const result: AICommandResult = {
  success: data.success,
  message: data.message,
  action: data.action,
  entityType: data.entityType,
  entityId: data.entityId
};

showAIConfirmationToast(result, {
  entityType: result.entityType || '',
  entityId: result.entityId,
  navigate
});
```

## AI Command Processor Response Format

The AI command processor returns responses with this structure:

```typescript
interface AICommandResult {
  success: boolean;
  message: string;
  action?: string;          // e.g., 'create', 'update', 'delete'
  entityType?: string;      // e.g., 'maintenance_task', 'purchase_order'
  entityId?: string;        // UUID of the created/updated item
}
```

## User Experience

### Before Enhancement
- AI creates a maintenance task
- User sees: "✅ Successfully created maintenance task"
- User has to manually navigate to maintenance page to find the task

### After Enhancement
- AI creates a maintenance task
- User sees: "✅ Successfully created maintenance task" with a "View maintenance task" button
- User clicks the button and is taken directly to the task details

## Testing

### Manual Testing
1. Use the AI Assistant to create items:
   - "Create a maintenance task for fixing the broken window"
   - "Create a purchase order for all low stock items"
   - "Add a new property called Sunset Villa"

2. Verify that success messages include clickable buttons
3. Click the buttons to ensure navigation works correctly
4. Test error cases to ensure they don't show action buttons

### Automated Testing
Run the test script in the browser console:
```javascript
// Load the test script from src/test/test-clickable-confirmations.js
testClickableConfirmations();
```

## Technical Details

### Navigation Logic
- Uses React Router's `useNavigate` hook for programmatic navigation
- Supports both query parameters and path parameters based on entity type
- Falls back to dashboard for unknown entity types

### Toast Configuration
- Uses Sonner toast library with action button support
- Longer duration (8000ms) for actionable toasts
- Consistent styling with existing UI components
- External link icon to indicate navigation action

### Error Handling
- Gracefully handles missing entity information
- Falls back to regular toast for incomplete data
- Maintains backward compatibility with existing code

## Future Enhancements

### Potential Improvements
1. **Deep linking**: Navigate to specific sections within pages (e.g., specific tab in maintenance details)
2. **Batch operations**: Support for multiple created items with navigation to filtered views
3. **Context awareness**: Show different actions based on current page context
4. **Animation**: Smooth transitions when navigating from toast clicks

### Additional Entity Types
- Bookings
- Contacts
- Collections
- Invoices
- Automation rules

## Troubleshooting

### Common Issues

**Toast doesn't show action button**
- Check that `entityType` and `entityId` are provided in the AI response
- Verify that `navigationConfig` is passed to `showAIConfirmationToast`

**Navigation doesn't work**
- Ensure React Router's `useNavigate` hook is properly imported
- Check browser console for navigation errors
- Verify the target page exists and is accessible

**Wrong navigation path**
- Check the `getNavigationPath` function in `src/lib/utils.ts`
- Verify entity type matches expected values
- Update path mapping if routes have changed

## Related Files

- `src/lib/utils.ts` - Core implementation
- `src/components/dashboard/AiCommandCenter.tsx` - Dashboard AI integration
- `src/components/maintenance/AiMaintenanceDialog.tsx` - Maintenance AI integration
- `src/test/test-clickable-confirmations.js` - Test script
- `docs/clickable-ai-confirmations.md` - This documentation
