import { createClient } from '@supabase/supabase-js'
import puppeteer from 'puppeteer'
import readline from 'readline'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)
const APP_URL = 'http://localhost:8080'

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function askQuestion(question) {
  return new Promise(resolve => rl.question(question, resolve))
}

async function testInvitationFlow() {
  let browser
  
  try {
    console.log('🧪 Interactive Invitation Flow Test')
    console.log('===================================')
    
    // Get credentials
    const email = await askQuestion('Enter your email: ')
    const password = await askQuestion('Enter your password: ')
    
    console.log('\n🚀 Starting browser automation test...')
    
    // Launch browser
    browser = await puppeteer.launch({ 
      headless: false,
      defaultViewport: { width: 1280, height: 720 },
      slowMo: 300,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })
    
    const page = await browser.newPage()
    
    // Step 1: Navigate and login
    console.log('\n1. Navigating to application...')
    await page.goto(APP_URL, { waitUntil: 'networkidle0' })
    
    console.log('2. Attempting login...')
    await page.waitForSelector('input[type="email"]', { timeout: 10000 })
    
    await page.type('input[type="email"]', email)
    await page.type('input[type="password"]', password)
    
    // Click login button
    await page.click('button[type="submit"]')
    await page.waitForTimeout(3000)
    
    const currentUrl = page.url()
    console.log('Current URL:', currentUrl)
    
    if (currentUrl.includes('dashboard') || currentUrl.includes('teams') || currentUrl.includes('properties')) {
      console.log('✅ Login successful!')
      
      // Step 2: Database verification
      console.log('\n3. Checking database state...')
      
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('email', email)
        .single()
      
      if (profileError) {
        console.error('❌ Profile not found:', profileError.message)
      } else {
        console.log('✅ Profile found:')
        console.log(`   Name: ${profile.first_name} ${profile.last_name}`)
        console.log(`   Role: ${profile.role}`)
        console.log(`   Email: ${profile.email}`)
        
        // Check teams
        const { data: teams, error: teamsError } = await supabase
          .rpc('get_user_teams', { user_id: profile.id })
        
        if (teamsError) {
          console.error('❌ Error fetching teams:', teamsError.message)
        } else {
          console.log(`✅ Teams: ${teams?.length || 0} found`)
          teams?.forEach(team => {
            console.log(`   - ${team.team_name} (${team.user_role})`)
          })
        }
        
        // Check team memberships
        const { data: memberships, error: memberError } = await supabase
          .from('team_members')
          .select('*, teams!inner(name)')
          .eq('user_id', profile.id)
        
        if (!memberError && memberships?.length > 0) {
          console.log(`✅ Team memberships: ${memberships.length} found`)
          memberships.forEach(m => {
            console.log(`   - ${m.teams.name}: ${m.role}`)
          })
        }
      }
      
      // Step 3: UI verification
      console.log('\n4. Checking UI elements...')
      
      // Wait for user to verify what they see
      await askQuestion('\nPress Enter after you\'ve checked what you can see in the UI...')
      
      // Navigate to teams page
      console.log('\n5. Testing teams page navigation...')
      try {
        await page.goto(`${APP_URL}/#/teams`, { waitUntil: 'networkidle0' })
        await page.waitForTimeout(2000)
        
        const teamsPageText = await page.evaluate(() => document.body.textContent)
        console.log('Teams page loaded, contains "team":', teamsPageText.toLowerCase().includes('team'))
        
      } catch (error) {
        console.error('Error navigating to teams page:', error.message)
      }
      
      await askQuestion('\nPress Enter after checking the teams page...')
      
      // Navigate to dashboard/properties
      console.log('\n6. Testing dashboard/properties navigation...')
      try {
        await page.goto(`${APP_URL}/#/dashboard`, { waitUntil: 'networkidle0' })
        await page.waitForTimeout(2000)
        
        const dashboardText = await page.evaluate(() => document.body.textContent)
        console.log('Dashboard loaded, contains property data:', 
          dashboardText.toLowerCase().includes('property') || 
          dashboardText.toLowerCase().includes('maintenance'))
        
      } catch (error) {
        console.error('Error navigating to dashboard:', error.message)
      }
      
      await askQuestion('\nPress Enter after checking the dashboard...')
      
    } else {
      console.log('❌ Login failed or unexpected redirect')
      
      // Check for error messages
      const bodyText = await page.evaluate(() => document.body.textContent)
      console.log('Page content includes error keywords:', 
        bodyText.includes('error') || bodyText.includes('invalid') || bodyText.includes('failed'))
    }
    
    const shouldTestInvitation = await askQuestion('\nDo you want to test creating an invitation? (y/n): ')
    
    if (shouldTestInvitation.toLowerCase() === 'y') {
      console.log('\n7. Testing invitation creation...')
      
      // Try to find and click invite button
      try {
        const inviteSelectors = [
          'button:has-text("Invite")',
          'a:has-text("Invite")', 
          '[data-testid*="invite"]',
          'button[aria-label*="invite"]'
        ]
        
        let inviteButton = null
        for (const selector of inviteSelectors) {
          try {
            inviteButton = await page.$(selector)
            if (inviteButton) break
          } catch (e) {}
        }
        
        if (inviteButton) {
          console.log('✅ Found invite button, clicking...')
          await inviteButton.click()
          await page.waitForTimeout(2000)
          
          await askQuestion('Press Enter after testing the invitation flow...')
        } else {
          console.log('ℹ️ No invite button found. Try manually navigating to invite functionality.')
          await askQuestion('Press Enter when done...')
        }
        
      } catch (error) {
        console.log('Error testing invitation creation:', error.message)
      }
    }
    
    console.log('\n✅ Interactive test completed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    if (browser) await browser.close()
    rl.close()
  }
}

testInvitationFlow()