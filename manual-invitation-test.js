import { createClient } from '@supabase/supabase-js'
import readline from 'readline'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function askQuestion(question) {
  return new Promise(resolve => rl.question(question, resolve))
}

async function manualTest() {
  try {
    console.log('🔍 Manual Invitation Flow Database Test')
    console.log('======================================')
    
    const email = await askQuestion('Enter your email: ')
    const password = await askQuestion('Enter your password: ')
    
    console.log('\n1. Testing authentication...')
    
    // Try to sign in
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (authError) {
      console.error('❌ Authentication failed:', authError.message)
      
      if (authError.message.includes('Email not confirmed')) {
        console.log('ℹ️ Email needs to be confirmed. Check your email for confirmation link.')
      } else if (authError.message.includes('Invalid login credentials')) {
        console.log('ℹ️ Invalid credentials. Double-check your email and password.')
      }
      
      rl.close()
      return
    }
    
    console.log('✅ Authentication successful!')
    console.log(`   User ID: ${authData.user.id}`)
    console.log(`   Email: ${authData.user.email}`)
    
    console.log('\n2. Checking profile data...')
    
    // Get profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single()
    
    if (profileError) {
      console.error('❌ Profile not found:', profileError.message)
    } else {
      console.log('✅ Profile found:')
      console.log(`   Name: ${profile.first_name || 'Missing'} ${profile.last_name || 'Missing'}`)
      console.log(`   Role: ${profile.role || 'Missing'}`)
      console.log(`   Super Admin: ${profile.is_super_admin || false}`)
      console.log(`   Phone: ${profile.phone || 'Not set'}`)
      console.log(`   Timezone: ${profile.timezone || 'Not set'}`)
    }
    
    console.log('\n3. Checking team access...')
    
    // Test RPC function
    const { data: userTeams, error: teamsRpcError } = await supabase
      .rpc('get_user_teams', { user_id: authData.user.id })
    
    if (teamsRpcError) {
      console.error('❌ RPC function error:', teamsRpcError.message)
    } else {
      console.log(`✅ RPC function working: ${userTeams?.length || 0} teams found`)
      userTeams?.forEach(team => {
        console.log(`   - ${team.team_name} (${team.user_role}) - ${team.member_count} members`)
      })
    }
    
    // Direct teams query
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
    
    if (teamsError) {
      console.error('❌ Teams query error:', teamsError.message)
    } else {
      console.log(`✅ Direct teams query: ${teams?.length || 0} teams accessible`)
      teams?.forEach(team => {
        console.log(`   - ${team.name} (Owner: ${team.owner_id === authData.user.id ? 'You' : 'Other'})`)
      })
    }
    
    console.log('\n4. Checking team memberships...')
    
    const { data: memberships, error: memberError } = await supabase
      .from('team_members')
      .select('*, teams!inner(name)')
      .eq('user_id', authData.user.id)
    
    if (memberError) {
      console.error('❌ Team memberships error:', memberError.message)
    } else {
      console.log(`✅ Team memberships: ${memberships?.length || 0} found`)
      memberships?.forEach(m => {
        console.log(`   - ${m.teams.name}: ${m.role || 'No role'}`)
      })
    }
    
    console.log('\n5. Checking properties access...')
    
    const { data: properties, error: propertiesError } = await supabase
      .from('properties')
      .select('*')
    
    if (propertiesError) {
      console.error('❌ Properties query error:', propertiesError.message)
    } else {
      console.log(`✅ Properties accessible: ${properties?.length || 0} found`)
      properties?.slice(0, 3).forEach(prop => {
        console.log(`   - ${prop.name} (${prop.city || 'No city'})`)
      })
    }
    
    console.log('\n6. Checking maintenance requests...')
    
    const { data: maintenance, error: maintenanceError } = await supabase
      .from('maintenance_requests')
      .select('*')
    
    if (maintenanceError) {
      console.error('❌ Maintenance requests error:', maintenanceError.message)
    } else {
      console.log(`✅ Maintenance requests accessible: ${maintenance?.length || 0} found`)
      maintenance?.slice(0, 3).forEach(req => {
        console.log(`   - ${req.title} (${req.status})`)
      })
    }
    
    console.log('\n7. Checking invitations...')
    
    // Check invitations sent by user
    const { data: sentInvitations, error: sentError } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('invited_by', authData.user.id)
    
    if (!sentError) {
      console.log(`✅ Invitations sent: ${sentInvitations?.length || 0}`)
    }
    
    // Check invitations received by user
    const { data: receivedInvitations, error: receivedError } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('email', email)
    
    if (!receivedError) {
      console.log(`✅ Invitations received: ${receivedInvitations?.length || 0}`)
      receivedInvitations?.forEach(inv => {
        console.log(`   - ${inv.role} role in team ${inv.team_id} (${inv.status})`)
      })
    }
    
    // Sign out
    await supabase.auth.signOut()
    
    console.log('\n✅ Manual test completed!')
    console.log('\nNext steps:')
    console.log('1. Open http://localhost:8080 in your browser')
    console.log('2. Login with your credentials')
    console.log('3. Check if you can see:')
    console.log('   - Your profile information')
    console.log('   - Team data')
    console.log('   - Properties and maintenance requests')
    console.log('4. Try creating and accepting an invitation')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    rl.close()
  }
}

manualTest()