import puppeteer from 'puppeteer'

const APP_URL = 'http://localhost:8080'

async function testApp() {
  let browser
  
  try {
    console.log('🔍 Testing app state...')
    
    browser = await puppeteer.launch({ 
      headless: false,
      defaultViewport: { width: 1280, height: 720 },
      slowMo: 500
    })
    
    const page = await browser.newPage()
    
    // Navigate to app
    await page.goto(APP_URL, { waitUntil: 'networkidle0' })
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const currentUrl = page.url()
    console.log('Current URL:', currentUrl)
    
    // Check page content
    const pageText = await page.evaluate(() => document.body.textContent)
    
    // Check if login form is present
    const hasEmailInput = await page.$('input[type="email"]') !== null
    const hasPasswordInput = await page.$('input[type="password"]') !== null
    
    console.log('Has login form:', hasEmailInput && hasPasswordInput)
    console.log('Page contains "login":', pageText.toLowerCase().includes('login'))
    console.log('Page contains "dashboard":', pageText.toLowerCase().includes('dashboard'))
    console.log('Page contains "Andrew":', pageText.includes('Andrew'))
    console.log('Page contains team data:', pageText.toLowerCase().includes('team'))
    
    // Take screenshot
    await page.screenshot({ path: 'app-current-state.png', fullPage: true })
    console.log('📸 Screenshot saved: app-current-state.png')
    
    // If already logged in, let's check what's visible
    if (!hasEmailInput) {
      console.log('\n✅ Already logged in! Checking data visibility...')
      
      // Try to navigate to teams page
      try {
        await page.goto(`${APP_URL}/#/teams`, { waitUntil: 'networkidle0' })
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        const teamsPageText = await page.evaluate(() => document.body.textContent)
        console.log('Teams page contains team data:', teamsPageText.toLowerCase().includes('team'))
        
        await page.screenshot({ path: 'teams-page.png', fullPage: true })
        console.log('📸 Screenshot saved: teams-page.png')
        
      } catch (e) {
        console.log('Error navigating to teams page:', e.message)
      }
      
      // Try to navigate to properties/dashboard
      try {
        await page.goto(`${APP_URL}/#/dashboard`, { waitUntil: 'networkidle0' })
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        const dashboardText = await page.evaluate(() => document.body.textContent)
        console.log('Dashboard contains property data:', 
          dashboardText.toLowerCase().includes('property') || 
          dashboardText.toLowerCase().includes('maintenance'))
        
        await page.screenshot({ path: 'dashboard-page.png', fullPage: true })
        console.log('📸 Screenshot saved: dashboard-page.png')
        
      } catch (e) {
        console.log('Error navigating to dashboard:', e.message)
      }
    }
    
    console.log('\n🎯 Ready for invitation testing!')
    console.log('Use this invitation link:')
    console.log('🔗 http://localhost:8080/#/invite?token=8d18a33d-7bb0-46fa-9486-a4c6a1ea1787')
    
    // Keep browser open for manual testing
    console.log('\n⏳ Browser will stay open for 30 seconds for manual testing...')
    await new Promise(resolve => setTimeout(resolve, 30000))
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    if (browser) {
      await browser.close()
    }
  }
}

testApp().catch(console.error)