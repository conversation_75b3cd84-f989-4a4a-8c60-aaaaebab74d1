
import React, { useState, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2, Send, Sparkles, AlertCircle, Info, Mic } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { showAIConfirmationToast, type AICommandResult } from '@/lib/utils';

// Use the AICommandResult interface from utils
type CommandResult = AICommandResult;

const AiCommandCenter: React.FC = () => {
  const [command, setCommand] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const [result, setResult] = useState<CommandResult | null>(null);
  const { authState } = useAuth();
  const navigate = useNavigate();
  const userId = authState.user?.id;

  const handleCommandChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCommand(e.target.value);
    // Clear previous results when input changes
    if (result) setResult(null);
  };

  const handleAudioInput = () => {
    if (!('webkitSpeechRecognition' in window)) {
      toast.error("Speech Recognition is not supported by your browser.");
      return;
    }

    if (!recognitionRef.current) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onstart = () => {
        setIsRecording(true);
        toast.info("Listening...");
      };

      recognitionRef.current.onresult = (event: SpeechRecognitionEvent) => {
        const transcript = event.results[0][0].transcript;
        setCommand(transcript);
        setIsRecording(false);
        toast.success("Transcription complete.");
      };

      recognitionRef.current.onerror = (event: SpeechRecognitionErrorEvent) => {
        setIsRecording(false);
        console.error("Speech recognition error:", event.error);
        toast.error(`Speech recognition error: ${event.error}`);
      };

      recognitionRef.current.onend = () => {
        setIsRecording(false);
      };
    }

    if (isRecording) {
      recognitionRef.current.stop();
    } else {
      recognitionRef.current.start();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!command.trim()) return;
    if (!userId) {
      toast.error("You must be logged in to use the AI assistant");
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      console.log("Sending command to AI:", command);

      // Call Supabase edge function with proper authentication
      const { data, error } = await supabase.functions.invoke('ai-command-processor', {
        body: {
          command,
          userId
        }
      });

      if (error) {
        throw new Error(error.message || 'Failed to process command');
      }

      console.log("Response from AI command processor:", data);

      // Add additional check for empty data
      if (!data) {
        throw new Error('No response received from AI command processor');
      }

      setResult(data);

      // Use the enhanced toast with navigation support
      showAIConfirmationToast(data, {
        entityType: data.entityType || '',
        entityId: data.entityId,
        navigate
      });

      // Still trigger refresh events for maintenance tasks to update the dashboard
      if (data.success && data.entityType === 'maintenance_task') {
        console.log('[AiCommandCenter] Maintenance task created, triggering refresh');
        setTimeout(() => {
          // Dispatch a custom event to refresh maintenance tasks
          window.dispatchEvent(new CustomEvent('force-refresh-maintenance'));

          // Also force a URL update to trigger a re-render
          const timestamp = Math.floor(Date.now() / 1000);
          window.history.replaceState(
            {},
            document.title,
            `${window.location.pathname}?refresh=${timestamp}`
          );
        }, 500);
      }

    } catch (error) {
      console.error("Error processing AI command:", error);
      const errorResult: CommandResult = {
        success: false,
        message: error instanceof Error ? error.message : 'An unexpected error occurred'
      };
      setResult(errorResult);
      showAIConfirmationToast(errorResult);
    } finally {
      setIsLoading(false);
    }
  };

  const getPlaceholderText = () => {
    const suggestions = [
      "Add a property named Ocean View at 123 Beach Rd with 3 bedrooms",
      "Create a new kitchen collection with a budget of $500",
      "Add maintenance task to fix the broken sink at Mountain Cabin",
      "Create a purchase order for all low stock items",
      "We're down to only one Bath towel, we need a minimum of 12"
    ];

    return suggestions[Math.floor(Math.random() * suggestions.length)];
  };

  return (
    <div className="mb-2">
      <form onSubmit={handleSubmit} className="flex gap-1.5">
        <div className="flex items-center gap-1 px-2 py-1 bg-muted/30 rounded-md">
          <Sparkles className="h-3 w-3 text-primary" />
          <span className="text-xs font-medium text-muted-foreground">AI</span>
        </div>
        <Input
          placeholder={`Try: "${getPlaceholderText()}"`}
          value={command}
          onChange={handleCommandChange}
          className="flex-1 h-7 text-xs border-0 bg-muted/30"
          disabled={isLoading}
        />
        <Button type="button" size="sm" className="h-7 w-7 p-0" onClick={handleAudioInput} disabled={isLoading}>
          {isRecording ? (
            <Loader2 className="h-3 w-3 animate-spin" />
          ) : (
            <Mic className="h-3 w-3" />
          )}
        </Button>
        <Button type="submit" size="sm" className="h-7 w-7 p-0" disabled={!command.trim() || isLoading}>
          {isLoading ? (
            <Loader2 className="h-3 w-3 animate-spin" />
          ) : (
            <Send className="h-3 w-3" />
          )}
        </Button>
      </form>

      {result && (
        <div className={`mt-1 p-1.5 rounded text-xs ${
          result.success ? 'bg-green-50 dark:bg-green-950/50 text-green-700 dark:text-green-300' : 'bg-red-50 dark:bg-red-950/50 text-red-700 dark:text-red-300'
        }`}>
          <div className="flex items-center gap-1">
            {result.success ? (
              <Sparkles className="h-3 w-3 flex-shrink-0" />
            ) : (
              <AlertCircle className="h-3 w-3 flex-shrink-0" />
            )}
            <p className="text-xs leading-tight">{result.message}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AiCommandCenter;
