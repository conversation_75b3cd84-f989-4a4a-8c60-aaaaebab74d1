import React, { useCallback } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  Building2,
  Wrench,
  Package,
  ShoppingCart,
  AlertTriangle,
  Users,
  Settings,
  BarChart3,
  LogOut,
  User,
  Calendar
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissionsFixed';
import { PermissionType } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/shadcn-tooltip';
import { useNavigationRefresh } from '@/hooks/useNavigationRefresh';

interface NavItem {
  name: string;
  path: string;
  icon: React.ElementType;
  requiredPermission?: PermissionType;
  alwaysShow?: boolean;
}

interface VerticalSidebarProps {
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

const VerticalSidebar: React.FC<VerticalSidebarProps> = ({
  collapsed = false,
  onToggleCollapse
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { authState } = useAuth();
  const { hasPermission, isAdmin } = usePermissions();
  const { refreshRouteData } = useNavigationRefresh();

  const isActiveRoute = (path: string) => location.pathname === path ||
    (path !== '/' && location.pathname.startsWith(path));

  // Custom navigation handler that refreshes data when navigating
  const handleNavigation = useCallback(async (path: string, e: React.MouseEvent) => {
    e.preventDefault(); // Prevent default link behavior

    console.log(`[VerticalSidebar] Navigating to ${path} with data refresh`);

    try {
      // First refresh the data for the route
      console.log(`[VerticalSidebar] Refreshing data for ${path}`);
      await refreshRouteData(path);
      console.log(`[VerticalSidebar] Data refresh complete for ${path}`);

      // Then navigate to the route
      console.log(`[VerticalSidebar] Navigating to ${path}`);
      navigate(path);
      console.log(`[VerticalSidebar] Navigation complete to ${path}`);

      // Dispatch a custom event to notify that navigation has occurred
      window.dispatchEvent(new CustomEvent('stayfu-navigation-occurred', {
        detail: {
          from: location.pathname,
          to: path,
          timestamp: Date.now()
        }
      }));
    } catch (error) {
      console.error(`[VerticalSidebar] Error during navigation to ${path}:`, error);

      // Navigate anyway to ensure the user can still use the app
      navigate(path);
    }
  }, [navigate, refreshRouteData, location.pathname]);

  // Define navigation items with required permissions
  const navigationItems: NavItem[] = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: LayoutDashboard,
      alwaysShow: true // Everyone can see dashboard
    },
    {
      name: 'Operations',
      path: '/operations',
      icon: BarChart3,
      alwaysShow: true // Everyone can see operations
    },
    {
      name: 'Properties',
      path: '/properties',
      icon: Building2,
      requiredPermission: PermissionType.MANAGE_PROPERTIES
    },
    {
      name: 'Maintenance',
      path: '/maintenance',
      icon: Wrench,
      requiredPermission: PermissionType.VIEW_MAINTENANCE
    },
    {
      name: 'Task Automation',
      path: '/maintenance/automation',
      icon: Calendar,
      requiredPermission: PermissionType.VIEW_MAINTENANCE
    },
    {
      name: 'Inventory',
      path: '/inventory',
      icon: Package,
      requiredPermission: PermissionType.VIEW_INVENTORY
    },
    {
      name: 'Purchase Orders',
      path: '/purchase-orders',
      icon: ShoppingCart,
      requiredPermission: PermissionType.VIEW_PURCHASE_ORDERS
    },
    {
      name: 'Damages',
      path: '/damages',
      icon: AlertTriangle,
      requiredPermission: PermissionType.VIEW_DAMAGE_REPORTS
    },
    {
      name: 'Teams',
      path: '/teams',
      icon: Users,
      requiredPermission: PermissionType.VIEW_TEAM
    },
    // Admin link - only for super admins
    {
      name: 'Admin',
      path: '/admin',
      icon: User,
      // No permission required - we'll filter it manually
    },
    {
      name: 'Settings',
      path: '/settings',
      icon: Settings,
      alwaysShow: true // Everyone can see settings
    }
  ];

  // Add debug items for admins and property managers
  // REBUILT: Use user metadata instead of profile
  const userRole = authState.user?.user_metadata?.role || 'property_manager';
  if (isAdmin() || userRole === 'property_manager') {
    navigationItems.push({
      name: 'Debug',
      path: '/debug/data-loading',
      icon: () => <span className="text-xs font-mono">🐛</span>,
      alwaysShow: true
    });
  }

  // Filter navigation items based on user permissions
  const visibleNavItems = navigationItems.filter(item => {
    // REBUILT: Use user metadata instead of profile
    const userRole = authState.user?.user_metadata?.role || 'property_manager';
    const isSuper = authState.user?.user_metadata?.is_super_admin === true;

    // Special case for Admin link - only super admins can see it
    if (item.name === 'Admin') {
      return isSuper;
    }

    // Always show items marked as alwaysShow
    if (item.alwaysShow) return true;

    // Admins can see everything
    if (isAdmin()) return true;

    // Property managers can see everything
    if (userRole === 'property_manager') return true;

    // Service providers should see all relevant navigation items
    if (userRole === 'service_provider') {
      // Always show these items for service providers
      if (['Dashboard', 'Properties', 'Maintenance', 'Inventory', 'Purchase Orders', 'Damages', 'Teams'].includes(item.name)) {
        return true;
      }
    }

    // Check specific permissions for other users
    if (item.requiredPermission) {
      return hasPermission(item.requiredPermission);
    }

    return false;
  });

  // Remove settings from main navigation and add it to the bottom
  const mainNavItems = visibleNavItems.filter(item => item.name !== 'Settings');
  const settingsItem = visibleNavItems.find(item => item.name === 'Settings');

  const { signOut } = useAuth();
  const handleSignOut = async () => {
    try {
      // Clear any cached data from localStorage
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('cache:')) {
          localStorage.removeItem(key);
        }
      });

      // Clear sessionStorage
      sessionStorage.clear();

      // Call the signOut function
      await signOut();

      // Force redirect to login page with hash routing
      setTimeout(() => {
        window.location.href = '/#/login';
      }, 300);
    } catch (error) {
      console.error('Error during sign out:', error);
      // Force redirect even on error
      window.location.href = '/#/login';
    }
  };

  return (
    <aside className={cn(
      "sidebar h-screen flex-shrink-0 transition-all duration-300 flex flex-col fixed top-0 left-0 z-40",
      collapsed ? "w-16" : "w-64"
    )} style={{ width: collapsed ? '4rem' : '16rem' }}>
      <div className="p-4 flex items-center justify-between border-b border-[hsl(var(--sidebar-border))] flex-shrink-0">
        <a href="/dashboard" className="flex items-center gap-2" onClick={(e) => handleNavigation('/dashboard', e)}>
          <img
            src={collapsed ? "/icons/icon-32x32.png" : "/icons/logo.png"}
            alt="StayFu Logo"
            className={cn(
              "object-contain transition-all duration-300",
              collapsed
                ? "h-8 w-8"
                : "h-8 w-8 bg-white rounded-full p-1"
            )}
          />
          {!collapsed && <span className="text-lg font-semibold text-[hsl(var(--sidebar-foreground))]">StayFu</span>}
        </a>
        {!collapsed ? (
          <Button
            variant="ghost"
            size="icon"
            className="text-[hsl(var(--sidebar-foreground))] hover:bg-[hsl(var(--sidebar-accent))]"
            onClick={onToggleCollapse}
          >
            <span className="sr-only">Collapse sidebar</span>
            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
            </svg>
          </Button>
        ) : (
          <Button
            variant="ghost"
            size="icon"
            className="text-[hsl(var(--sidebar-foreground))] hover:bg-[hsl(var(--sidebar-accent))]"
            onClick={onToggleCollapse}
          >
            <span className="sr-only">Expand sidebar</span>
            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6.1584 3.13514C5.95694 3.32401 5.94673 3.64042 6.1356 3.84188L9.565 7.49991L6.1356 11.1579C5.94673 11.3594 5.95694 11.6758 6.1584 11.8647C6.35985 12.0535 6.67627 12.0433 6.86514 11.8419L10.6151 7.84188C10.7954 7.64955 10.7954 7.35027 10.6151 7.15794L6.86514 3.15794C6.67627 2.95648 6.35985 2.94628 6.1584 3.13514Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
            </svg>
          </Button>
        )}
      </div>

      {/* Main navigation */}
      <nav className="p-3 flex-grow overflow-y-auto">
        <ul className="space-y-1">
          {mainNavItems.map((item) => {
            const Icon = item.icon;
            return (
              <li key={item.path}>
                {collapsed ? (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={item.path}
                          className={cn(
                            "flex items-center justify-center px-3 py-2 rounded-md transition-colors",
                            isActiveRoute(item.path)
                              ? "bg-[hsl(var(--sidebar-accent))] text-[hsl(var(--sidebar-accent-foreground))]"
                              : "text-[hsl(var(--sidebar-foreground))]/80 hover:bg-[hsl(var(--sidebar-accent))]/20 hover:text-[hsl(var(--sidebar-foreground))]"
                          )}
                          onClick={(e) => handleNavigation(item.path, e)}
                        >
                          <Icon size={20} />
                        </a>
                      </TooltipTrigger>
                      <TooltipContent side="right">
                        <p>{item.name}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : (
                  <a
                    href={item.path}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                      isActiveRoute(item.path)
                        ? "bg-[hsl(var(--sidebar-accent))] text-[hsl(var(--sidebar-accent-foreground))]"
                        : "text-[hsl(var(--sidebar-foreground))]/80 hover:bg-[hsl(var(--sidebar-accent))]/20 hover:text-[hsl(var(--sidebar-foreground))]"
                    )}
                    onClick={(e) => handleNavigation(item.path, e)}
                  >
                    <Icon size={20} />
                    <span className="text-sm font-medium">{item.name}</span>
                  </a>
                )}
              </li>
            );
          })}
        </ul>
      </nav>

      {/* User profile and settings at bottom */}
      <div className="mt-auto border-t border-[hsl(var(--sidebar-border))] p-3 flex-shrink-0">
        <ul className="space-y-1">
          {settingsItem && (
            <li key={settingsItem.path}>
              {collapsed ? (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <a
                        href={settingsItem.path}
                        className={cn(
                          "flex items-center justify-center px-3 py-2 rounded-md transition-colors",
                          isActiveRoute(settingsItem.path)
                            ? "bg-[hsl(var(--sidebar-accent))] text-[hsl(var(--sidebar-accent-foreground))]"
                            : "text-[hsl(var(--sidebar-foreground))]/80 hover:bg-[hsl(var(--sidebar-accent))]/20 hover:text-[hsl(var(--sidebar-foreground))]"
                        )}
                        onClick={(e) => handleNavigation(settingsItem.path, e)}
                      >
                        <Settings size={20} />
                      </a>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <p>Settings</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                <a
                  href={settingsItem.path}
                  className={cn(
                    "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                    isActiveRoute(settingsItem.path)
                      ? "bg-[hsl(var(--sidebar-accent))] text-[hsl(var(--sidebar-accent-foreground))]"
                      : "text-[hsl(var(--sidebar-foreground))]/80 hover:bg-[hsl(var(--sidebar-accent))]/20 hover:text-[hsl(var(--sidebar-foreground))]"
                  )}
                  onClick={(e) => handleNavigation(settingsItem.path, e)}
                >
                  <Settings size={20} />
                  <span className="text-sm font-medium">Settings</span>
                </a>
              )}
            </li>
          )}

          <li>
            {collapsed ? (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={handleSignOut}
                      className="w-full flex items-center justify-center px-3 py-2 rounded-md transition-colors text-[hsl(var(--sidebar-foreground))]/80 hover:bg-[hsl(var(--sidebar-accent))]/20 hover:text-[hsl(var(--sidebar-foreground))]"
                      aria-label="Sign Out"
                    >
                      <LogOut size={20} />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>Sign Out</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <button
                onClick={handleSignOut}
                className="w-full flex items-center gap-3 px-3 py-2 rounded-md transition-colors text-[hsl(var(--sidebar-foreground))]/80 hover:bg-[hsl(var(--sidebar-accent))]/20 hover:text-[hsl(var(--sidebar-foreground))]"
                aria-label="Sign Out"
              >
                <LogOut size={20} />
                <span className="text-sm font-medium">Sign Out</span>
              </button>
            )}
          </li>

          <li className="pt-2">
            {collapsed ? (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <a href="/settings/account" className="flex items-center justify-center px-3 py-2" onClick={(e) => handleNavigation('/settings/account', e)}>
                      <Avatar className="h-8 w-8 bg-[hsl(var(--sidebar-primary))] ring-2 ring-[hsl(var(--sidebar-foreground))]/30">
                        {authState.profile?.avatar_url ? (
                          <AvatarImage src={authState.profile.avatar_url} alt={authState.profile?.first_name || 'User'} />
                        ) : null}
                        <AvatarFallback>{authState.profile?.first_name?.charAt(0) || 'U'}</AvatarFallback>
                      </Avatar>
                    </a>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>{authState.profile?.first_name || 'User'}</p>
                    <p className="text-xs opacity-70">
                      {authState.profile?.role === 'property_manager' ? 'Property Manager' :
                       authState.profile?.role === 'super_admin' ? 'Super Admin' :
                       authState.profile?.role === 'admin' ? 'Admin' :
                       authState.profile?.role === 'service_provider' ? 'Service Provider' :
                       authState.profile?.role === 'staff' ? 'Staff' :
                       authState.profile?.role || 'User'}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <div className="flex items-center gap-3 px-3 py-2">
                <a href="/settings/account" onClick={(e) => handleNavigation('/settings/account', e)}>
                  <Avatar className="h-8 w-8 bg-[hsl(var(--sidebar-primary))] ring-2 ring-[hsl(var(--sidebar-foreground))]/30">
                    {authState.profile?.avatar_url ? (
                      <AvatarImage src={authState.profile.avatar_url} alt={authState.profile?.first_name || 'User'} />
                    ) : null}
                    <AvatarFallback>{authState.profile?.first_name?.charAt(0) || 'U'}</AvatarFallback>
                  </Avatar>
                </a>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-[hsl(var(--sidebar-foreground))]">
                    {authState.profile?.first_name || 'User'}
                  </span>
                  <span className="text-xs text-[hsl(var(--sidebar-foreground))]/70">
                    {authState.profile?.role === 'property_manager' ? 'Property Manager' :
                     authState.profile?.role === 'super_admin' ? 'Super Admin' :
                     authState.profile?.role === 'admin' ? 'Admin' :
                     authState.profile?.role === 'service_provider' ? 'Service Provider' :
                     authState.profile?.role === 'staff' ? 'Staff' :
                     authState.profile?.role || 'User'}
                  </span>
                </div>
              </div>
            )}
          </li>
        </ul>
      </div>
    </aside>
  );
};

export default VerticalSidebar;
