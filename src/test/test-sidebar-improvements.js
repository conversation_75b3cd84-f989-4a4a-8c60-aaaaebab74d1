/**
 * Test script for sidebar improvements
 * Tests:
 * 1. Sidebar height adjustment without scrolling
 * 2. Logo switching between full logo and favicon when collapsed
 * 3. Proper flex layout behavior
 * 4. Optimized spacing and text sizes
 */

// Test sidebar height and scrolling behavior
function testSidebarHeight() {
  console.log('🧪 Testing sidebar height and scrolling...');
  
  const sidebar = document.querySelector('aside.sidebar');
  if (!sidebar) {
    console.error('❌ Sidebar not found');
    return false;
  }
  
  // Check if sidebar has proper height
  const sidebarHeight = sidebar.offsetHeight;
  const windowHeight = window.innerHeight;
  
  console.log(`📏 Sidebar height: ${sidebarHeight}px, Window height: ${windowHeight}px`);
  
  if (Math.abs(sidebarHeight - windowHeight) > 5) {
    console.error('❌ Sidebar height does not match window height');
    return false;
  }
  
  // Check if sidebar has overflow-y-auto removed from main container
  const sidebarStyles = window.getComputedStyle(sidebar);
  if (sidebarStyles.overflowY === 'auto') {
    console.error('❌ Sidebar still has overflow-y: auto on main container');
    return false;
  }
  
  // Check if navigation section has overflow-y-auto
  const nav = sidebar.querySelector('nav');
  if (!nav) {
    console.error('❌ Navigation section not found');
    return false;
  }
  
  const navStyles = window.getComputedStyle(nav);
  if (navStyles.overflowY !== 'auto') {
    console.error('❌ Navigation section should have overflow-y: auto');
    return false;
  }
  
  console.log('✅ Sidebar height and scrolling behavior correct');
  return true;
}

// Test logo switching behavior
function testLogoSwitching() {
  console.log('🧪 Testing logo switching...');
  
  const logoImg = document.querySelector('aside.sidebar img[alt="StayFu Logo"]');
  if (!logoImg) {
    console.error('❌ Logo image not found');
    return false;
  }
  
  // Check current logo source
  const currentSrc = logoImg.src;
  console.log(`🖼️ Current logo source: ${currentSrc}`);
  
  // Find collapse button
  const collapseButton = document.querySelector('aside.sidebar button[aria-label*="sidebar" i], aside.sidebar button svg');
  if (!collapseButton) {
    console.error('❌ Collapse button not found');
    return false;
  }
  
  // Get the actual button element (might be nested)
  const button = collapseButton.closest('button');
  if (!button) {
    console.error('❌ Could not find button element');
    return false;
  }
  
  console.log('🔄 Testing logo switching by toggling sidebar...');
  
  // Store initial state
  const initialSrc = logoImg.src;
  const sidebar = document.querySelector('aside.sidebar');
  const initialWidth = sidebar.offsetWidth;
  
  // Click to toggle
  button.click();
  
  // Wait for transition
  setTimeout(() => {
    const newSrc = logoImg.src;
    const newWidth = sidebar.offsetWidth;
    
    console.log(`📏 Width changed from ${initialWidth}px to ${newWidth}px`);
    console.log(`🖼️ Logo changed from ${initialSrc} to ${newSrc}`);
    
    // Check if logo source changed appropriately
    const isCollapsed = newWidth < 100; // Collapsed should be around 64px
    const expectedSrc = isCollapsed ? '/icons/icon-32x32.png' : '/icons/logo.png';
    
    if (!newSrc.includes(expectedSrc)) {
      console.error(`❌ Logo source incorrect. Expected to contain: ${expectedSrc}, got: ${newSrc}`);
      return false;
    }
    
    console.log('✅ Logo switching behavior correct');
    
    // Toggle back
    button.click();
    
    return true;
  }, 500);
}

// Test flex layout behavior
function testFlexLayout() {
  console.log('🧪 Testing flex layout...');
  
  const sidebar = document.querySelector('aside.sidebar');
  if (!sidebar) {
    console.error('❌ Sidebar not found');
    return false;
  }
  
  // Check if sidebar is flex column
  const sidebarStyles = window.getComputedStyle(sidebar);
  if (sidebarStyles.display !== 'flex' || sidebarStyles.flexDirection !== 'column') {
    console.error('❌ Sidebar should be flex column');
    return false;
  }
  
  // Check header section
  const header = sidebar.querySelector('div:first-child');
  if (!header) {
    console.error('❌ Header section not found');
    return false;
  }
  
  const headerStyles = window.getComputedStyle(header);
  if (!headerStyles.flexShrink || headerStyles.flexShrink === '1') {
    console.error('❌ Header should have flex-shrink-0');
    return false;
  }
  
  // Check navigation section
  const nav = sidebar.querySelector('nav');
  if (!nav) {
    console.error('❌ Navigation section not found');
    return false;
  }
  
  const navStyles = window.getComputedStyle(nav);
  if (navStyles.flexGrow !== '1') {
    console.error('❌ Navigation should have flex-grow');
    return false;
  }
  
  // Check footer section
  const footer = sidebar.querySelector('div:last-child');
  if (!footer) {
    console.error('❌ Footer section not found');
    return false;
  }
  
  const footerStyles = window.getComputedStyle(footer);
  if (!footerStyles.flexShrink || footerStyles.flexShrink === '1') {
    console.error('❌ Footer should have flex-shrink-0');
    return false;
  }
  
  console.log('✅ Flex layout behavior correct');
  return true;
}

// Test spacing and text size optimizations
function testSpacingOptimization() {
  console.log('🧪 Testing spacing and text size optimizations...');

  const sidebar = document.querySelector('aside.sidebar');
  if (!sidebar) {
    console.error('❌ Sidebar not found');
    return false;
  }

  // Check navigation item spacing
  const navItems = sidebar.querySelectorAll('nav ul li a');
  if (navItems.length === 0) {
    console.error('❌ Navigation items not found');
    return false;
  }

  // Check if navigation items have optimized padding
  const firstNavItem = navItems[0];
  const navStyles = window.getComputedStyle(firstNavItem);
  const paddingY = parseFloat(navStyles.paddingTop) + parseFloat(navStyles.paddingBottom);

  console.log(`📏 Navigation item vertical padding: ${paddingY}px`);

  if (paddingY > 16) { // Should be around 12px (py-1.5 = 6px top + 6px bottom)
    console.error('❌ Navigation items have too much vertical padding');
    return false;
  }

  // Check text size
  const textSpan = sidebar.querySelector('nav span');
  if (textSpan) {
    const textStyles = window.getComputedStyle(textSpan);
    const fontSize = parseFloat(textStyles.fontSize);

    console.log(`📝 Navigation text size: ${fontSize}px`);

    if (fontSize > 14) { // Should be around 12px (text-xs)
      console.error('❌ Navigation text is too large');
      return false;
    }
  }

  // Check icon sizes
  const icons = sidebar.querySelectorAll('nav svg');
  if (icons.length > 0) {
    const iconSize = icons[0].getAttribute('width') || icons[0].getAttribute('size');
    console.log(`🎯 Icon size: ${iconSize}px`);

    if (parseInt(iconSize) > 20) { // Should be 18px
      console.error('❌ Icons are too large');
      return false;
    }
  }

  console.log('✅ Spacing and text size optimizations correct');
  return true;
}

// Run all tests
function runSidebarTests() {
  console.log('🚀 Starting sidebar improvement tests...');
  
  const tests = [
    testSidebarHeight,
    testFlexLayout,
    testLogoSwitching,
    testSpacingOptimization
  ];
  
  let passed = 0;
  let total = tests.length;
  
  tests.forEach((test, index) => {
    try {
      if (test()) {
        passed++;
      }
    } catch (error) {
      console.error(`❌ Test ${index + 1} failed with error:`, error);
    }
  });
  
  console.log(`\n📊 Test Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All sidebar improvement tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Check the console for details.');
  }
}

// Auto-run tests when script loads
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runSidebarTests);
  } else {
    runSidebarTests();
  }
}

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testSidebarHeight,
    testLogoSwitching,
    testFlexLayout,
    testSpacingOptimization,
    runSidebarTests
  };
}
