import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function createAutoConfirmInvitation() {
  console.log('🎯 Creating invitation with auto email confirmation')
  console.log('================================================')
  
  // Login as team owner
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'Newsig1!!!'
  })
  
  if (authError) {
    console.error('❌ Login failed:', authError.message)
    return
  }
  
  console.log('✅ Logged in as team owner')
  
  // Get teams
  const { data: teams } = await supabase.from('teams').select('*')
  
  if (teams && teams.length > 0) {
    // Create a new invitation
    const invitationToken = crypto.randomUUID()
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7)
    
    const { error: inviteError } = await supabase
      .from('team_invitations')
      .insert({
        team_id: teams[0].id,
        email: '<EMAIL>',
        role: 'service_provider',
        invited_by: authData.user.id,
        token: invitationToken,
        expires_at: expiresAt.toISOString(),
        status: 'pending'
      })
    
    if (!inviteError) {
      console.log('✅ AUTO-CONFIRM INVITATION CREATED!')
      console.log('')
      console.log('🔗 INVITATION LINK (NO EMAIL CONFIRMATION REQUIRED):')
      console.log(`http://localhost:8081/#/invite?token=${invitationToken}`)
      console.log('')
      console.log('📋 TEST INSTRUCTIONS:')
      console.log('1. Open the invitation link above')
      console.log('2. Register with: <EMAIL>')
      console.log('3. Use any password (e.g., testpassword123)')
      console.log('4. Email will be AUTO-CONFIRMED - no email verification needed!')
      console.log('5. You should be immediately logged in and see team data')
      console.log('')
      console.log('✨ This uses the edge function that bypasses email confirmation')
      console.log('   for users coming from invitation links!')
    } else {
      console.error('❌ Failed to create invitation:', inviteError)
    }
  } else {
    console.error('❌ No teams found')
  }
  
  await supabase.auth.signOut()
}

createAutoConfirmInvitation().catch(console.error)