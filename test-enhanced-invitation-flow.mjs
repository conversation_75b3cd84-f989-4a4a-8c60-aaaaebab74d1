import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testEnhancedInvitationFlow() {
  console.log('🚀 Testing Enhanced Invitation Flow')
  console.log('===================================')
  console.log('')
  
  // Login as team owner
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'Newsig1!!!'
  })
  
  if (authError) {
    console.error('❌ Login failed:', authError.message)
    return
  }
  
  console.log('✅ Logged in as team owner')
  
  // Get teams
  const { data: teams } = await supabase.from('teams').select('*')
  
  if (teams && teams.length > 0) {
    const teamId = teams[0].id
    const teamName = teams[0].name
    
    console.log(`📋 Testing with team: ${teamName} (${teamId})`)
    console.log('')
    
    // Test Case 1: New user invitation
    console.log('🧪 TEST CASE 1: New User Invitation')
    console.log('==================================')
    
    const newUserToken = crypto.randomUUID()
    const newUserEmail = `newuser-${Date.now()}@example.com`
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7)
    
    await supabase.from('team_invitations').insert({
      team_id: teamId,
      email: newUserEmail,
      role: 'service_provider',
      invited_by: authData.user.id,
      token: newUserToken,
      expires_at: expiresAt.toISOString(),
      status: 'pending'
    })
    
    console.log('✅ New user invitation created')
    console.log(`🔗 http://localhost:8081/#/invite?token=${newUserToken}`)
    console.log(`📧 Email: ${newUserEmail}`)
    console.log(`👤 Role: service_provider (auto-selected)`)
    console.log(`🎯 Expected: No email confirmation required, immediate login`)
    console.log('')
    
    // Test Case 2: Existing user invitation (different team)
    console.log('🧪 TEST CASE 2: Existing User Invitation')
    console.log('=======================================')
    
    const existingUserToken = crypto.randomUUID()
    const existingUserEmail = '<EMAIL>' // User that might already exist
    
    await supabase.from('team_invitations').insert({
      team_id: teamId,
      email: existingUserEmail,
      role: 'staff',
      invited_by: authData.user.id,
      token: existingUserToken,
      expires_at: expiresAt.toISOString(),
      status: 'pending'
    })
    
    console.log('✅ Existing user invitation created')
    console.log(`🔗 http://localhost:8081/#/invite?token=${existingUserToken}`)
    console.log(`📧 Email: ${existingUserEmail}`)
    console.log(`👤 Role: staff (auto-selected)`)
    console.log(`🎯 Expected: If user exists, auto-add to team and show "Welcome back!"`)
    console.log('')
    
    // Test Case 3: Already a team member
    console.log('🧪 TEST CASE 3: Already Team Member')
    console.log('==================================')
    
    const memberToken = crypto.randomUUID()
    const memberEmail = '<EMAIL>' // Team owner - already a member
    
    await supabase.from('team_invitations').insert({
      team_id: teamId,
      email: memberEmail,
      role: 'admin',
      invited_by: authData.user.id,
      token: memberToken,
      expires_at: expiresAt.toISOString(),
      status: 'pending'
    })
    
    console.log('✅ Team member invitation created')
    console.log(`🔗 http://localhost:8081/#/invite?token=${memberToken}`)
    console.log(`📧 Email: ${memberEmail}`)
    console.log(`👤 Role: admin (auto-selected)`)
    console.log(`🎯 Expected: "You are already a member of this team!"`)
    console.log('')
    
    console.log('🎯 ENHANCED FEATURES IMPLEMENTED:')
    console.log('=================================')
    console.log('✅ 1. Auto email confirmation bypass for invitation users')
    console.log('✅ 2. Existing user detection by email address')
    console.log('✅ 3. Auto-add existing users to invited team')
    console.log('✅ 4. Role auto-selection based on invitation')
    console.log('✅ 5. Smart messaging for different user scenarios')
    console.log('✅ 6. Immediate login after successful registration')
    console.log('✅ 7. Team membership validation')
    console.log('')
    
    console.log('📋 TESTING INSTRUCTIONS:')
    console.log('========================')
    console.log('1. Test each invitation link above')
    console.log('2. Notice the role is auto-selected')
    console.log('3. For new users: immediate login after registration')
    console.log('4. For existing users: smart detection and team addition')
    console.log('5. For team members: graceful "already member" handling')
    console.log('')
    
    console.log('🎉 All enhancements are ready for testing!')
    
  } else {
    console.error('❌ No teams found')
  }
  
  await supabase.auth.signOut()
}

testEnhancedInvitationFlow().catch(console.error)