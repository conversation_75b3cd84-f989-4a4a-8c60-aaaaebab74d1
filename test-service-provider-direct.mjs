import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testServiceProviderAccess() {
  console.log('🔍 Testing Service Provider Access Patterns')
  console.log('==========================================')
  
  const testUserId = 'ca7fd3fb-4575-48e5-a6de-f5cdcd156eab' // <EMAIL>
  
  try {
    console.log('👤 Testing user ID:', testUserId)
    
    // Test 1: RPC function call
    console.log('\n🔧 Test 1: RPC Function Call')
    const { data: rpcTasks, error: rpcError } = await supabase.rpc('get_maintenance_tasks_for_user', {
      p_user_id: testUserId
    })
    
    if (rpcError) {
      console.error('❌ RPC Error:', rpcError)
    } else {
      console.log(`✅ RPC returned ${rpcTasks?.length || 0} tasks`)
    }
    
    // Test 2: Direct query with RLS
    console.log('\n🔧 Test 2: Direct Query (RLS applied)')
    // Note: This won't work properly because we're not authenticated as the user
    const { data: directTasks, error: directError } = await supabase
      .from('maintenance_tasks')
      .select('*')
    
    if (directError) {
      console.error('❌ Direct query error:', directError)
    } else {
      console.log(`Direct query returned ${directTasks?.length || 0} tasks (but RLS not properly applied since we're not that user)`)
    }
    
    // Test 3: Check user's team memberships
    console.log('\n👥 Test 3: User Team Memberships')
    const { data: teamMemberships, error: teamError } = await supabase
      .from('team_members')
      .select('team_id, role')
      .eq('user_id', testUserId)
    
    if (teamError) {
      console.error('❌ Team membership error:', teamError)
    } else {
      console.log(`User has ${teamMemberships?.length || 0} team memberships`)
      teamMemberships?.forEach(tm => {
        console.log(`  - Team ID: ${tm.team_id}, Role: ${tm.role}`)
      })
    }
    
    // Test 4: Check accessible properties
    console.log('\n🏠 Test 4: Accessible Properties')
    const teamIds = teamMemberships?.map(tm => tm.team_id) || []
    
    if (teamIds.length > 0) {
      const { data: accessibleProps, error: propsError } = await supabase
        .from('team_properties')
        .select('property_id')
        .in('team_id', teamIds)
      
      if (propsError) {
        console.error('❌ Properties error:', propsError)
      } else {
        console.log(`User can access ${accessibleProps?.length || 0} properties through team membership`)
      }
    } else {
      console.log('User has no team memberships, so no property access')
    }
    
    // Test 5: Check tasks assigned to user directly
    console.log('\n🎯 Test 5: Tasks Assigned to User')
    const allTasks = directTasks || []
    const assignedTasks = allTasks.filter(task => 
      task.user_id === testUserId || 
      task.provider_id === testUserId || 
      task.assigned_to === testUserId
    )
    
    console.log(`User has ${assignedTasks.length} tasks assigned to them`)
    assignedTasks.forEach(task => {
      console.log(`  - "${task.title}" (${
        task.user_id === testUserId ? 'created by user' :
        task.provider_id === testUserId ? 'assigned as provider' :
        'assigned directly'
      })`)
    })
    
    // Test 6: Check if tasks have team/property associations
    console.log('\n🔗 Test 6: Task Associations')
    allTasks.forEach(task => {
      if (task.user_id === testUserId || task.provider_id === testUserId || task.assigned_to === testUserId) {
        console.log(`Task "${task.title}":`)
        console.log(`  - Property ID: ${task.property_id || 'NULL'}`)
        console.log(`  - Team ID: ${task.team_id || 'NULL'}`)
        console.log(`  - Created by: ${task.user_id}`)
        console.log(`  - Provider: ${task.provider_id || 'NULL'}`)
        console.log(`  - Assigned to: ${task.assigned_to || 'NULL'}`)
      }
    })
    
    console.log('\n📋 Summary:')
    console.log('===========')
    console.log(`- RPC function returns: ${rpcTasks?.length || 0} tasks`)
    console.log(`- User team memberships: ${teamMemberships?.length || 0}`)
    console.log(`- Accessible properties: ${teamIds.length > 0 ? 'checking...' : 0}`)
    console.log(`- Tasks assigned to user: ${assignedTasks.length}`)
    
    if (rpcTasks?.length === 0 && assignedTasks.length === 0) {
      console.log('✅ This user should see NO maintenance tasks')
    } else {
      console.log('⚠️  This user might see maintenance tasks that need investigation')
    }
    
  } catch (error) {
    console.error('❌ Test error:', error)
  }
}

testServiceProviderAccess().catch(console.error)