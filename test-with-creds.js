import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testWithCreds() {
  try {
    const email = '<EMAIL>'
    const password = 'Newsig1!!!'
    
    console.log('🔍 Testing invitation flow with your credentials')
    console.log('=============================================')
    
    console.log('\n1. Testing authentication...')
    
    // Try to sign in
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (authError) {
      console.error('❌ Authentication failed:', authError.message)
      return
    }
    
    console.log('✅ Authentication successful!')
    console.log(`   User ID: ${authData.user.id}`)
    console.log(`   Email: ${authData.user.email}`)
    
    console.log('\n2. Checking profile data...')
    
    // Get profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single()
    
    if (profileError) {
      console.error('❌ Profile not found:', profileError.message)
    } else {
      console.log('✅ Profile found:')
      console.log(`   Name: ${profile.first_name || 'Missing'} ${profile.last_name || 'Missing'}`)
      console.log(`   Role: ${profile.role || 'Missing'}`)
      console.log(`   Super Admin: ${profile.is_super_admin || false}`)
    }
    
    console.log('\n3. Checking team access...')
    
    // Test RPC function
    const { data: userTeams, error: teamsRpcError } = await supabase
      .rpc('get_user_teams', { user_id: authData.user.id })
    
    if (teamsRpcError) {
      console.error('❌ RPC function error:', teamsRpcError.message)
    } else {
      console.log(`✅ RPC function working: ${userTeams?.length || 0} teams found`)
      userTeams?.forEach(team => {
        console.log(`   - ${team.team_name} (${team.user_role}) - ${team.member_count} members`)
      })
    }
    
    // Direct teams query
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
    
    if (teamsError) {
      console.error('❌ Teams query error:', teamsError.message)
    } else {
      console.log(`✅ Direct teams query: ${teams?.length || 0} teams accessible`)
      teams?.forEach(team => {
        console.log(`   - ${team.name} (ID: ${team.id})`)
      })
    }
    
    console.log('\n4. Checking team memberships...')
    
    const { data: memberships, error: memberError } = await supabase
      .from('team_members')
      .select('*, teams!inner(name)')
      .eq('user_id', authData.user.id)
    
    if (memberError) {
      console.error('❌ Team memberships error:', memberError.message)
    } else {
      console.log(`✅ Team memberships: ${memberships?.length || 0} found`)
      memberships?.forEach(m => {
        console.log(`   - ${m.teams.name}: ${m.role || 'No role'}`)
      })
    }
    
    console.log('\n5. Testing invitation creation...')
    
    // Create a test invitation
    const testEmail = '<EMAIL>'
    const testRole = 'service_provider'
    
    if (teams && teams.length > 0) {
      const testTeam = teams[0]
      
      console.log(`Creating invitation for ${testEmail} to join team "${testTeam.name}"...`)
      
      // Generate invitation token
      const invitationToken = crypto.randomUUID()
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now
      
      const { data: invitation, error: inviteError } = await supabase
        .from('team_invitations')
        .insert({
          team_id: testTeam.id,
          email: testEmail,
          role: testRole,
          invited_by: authData.user.id,
          token: invitationToken,
          expires_at: expiresAt.toISOString(),
          status: 'pending'
        })
        .select()
        .single()
      
      if (inviteError) {
        console.error('❌ Error creating invitation:', inviteError.message)
      } else {
        console.log('✅ Test invitation created successfully!')
        console.log(`   Token: ${invitationToken}`)
        console.log(`   Team: ${testTeam.name}`)
        console.log(`   Role: ${testRole}`)
        console.log(`   Invitation URL: http://localhost:8080/#/invite?token=${invitationToken}`)
        
        console.log('\n📋 INVITATION LINK FOR TESTING:')
        console.log(`🔗 http://localhost:8080/#/invite?token=${invitationToken}`)
        console.log('\n   Use this link to test the invitation acceptance flow!')
      }
    } else {
      console.log('❌ No teams found to create invitation for')
    }
    
    // Sign out
    await supabase.auth.signOut()
    
    console.log('\n✅ Test completed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testWithCreds()